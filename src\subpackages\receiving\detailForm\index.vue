<template>
  <view class="detail-form-page">
    <!-- 头部信息 -->
    <view class="header-info">
      <view class="info-content">
        <view class="info-item">
          <view class="label1">到货计划编号</view>
          <view class="value1">{{ planDetails.planCode }}</view>
        </view>
        <view class="info-item">
          <text class="label">供应商名称：</text>
          <text class="value">{{ planDetails.supplierName }}</text>
        </view>
        <view class="info-item">
          <text class="label">企业前缀：</text>
          <text class="value">{{ planDetails.entPrefix }}</text>
        </view>
      </view>
      <view>
        <image
          style="width: 100rpx; height: 120rpx"
          src="../../../images/plan.png"
          mode="aspectFit"
        >
        </image>
      </view>
    </view>

    <!-- 计划信息折叠面板 -->
    <view class="detail-section">
      <view class="section-title">
        <text>计划信息</text>
        <view
          class="collapse-arrow"
          :class="{ expanded: isPlanExpanded }"
          @click="togglePlan"
        >
          <IconFont name="rect-down" size="16" color="#1A43AD" />
        </view>
      </view>
      <view class="collapse-content" v-show="isPlanExpanded">
        <view class="plan-details">
          <view class="info-item">
            <text class="info-label">物料名称（备）</text>
            <text class="info-value">{{ planDetails.materialName }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">计划数量</text>
            <text class="info-value"
              >{{ planDetails.planQuantity }}{{ planDetails.unitName }}</text
            >
          </view>
          <view class="info-item">
            <text class="info-label">计划交货时间</text>
            <text class="info-value">{{ planDetails.planDeliveryTime }}</text>
          </view>
          <view v-if="planDetails.deliveryRemarkEnabled" class="info-bz"
            >备注</view
          >
          <view
            v-if="planDetails.deliveryRemarkEnabled"
            class="info-textarea"
            >{{ planDetails.deliveryRemark }}</view
          >
        </view>
      </view>
    </view>

    <!-- 供货信息折叠面板 -->
    <view class="detail-section">
      <view class="section-title">
        <text>货物信息</text>
        <view
          class="collapse-arrow"
          :class="{ expanded: isSupplyExpanded }"
          @click="toggleSupply"
        >
          <IconFont name="rect-down" size="16" color="#1A43AD" />
        </view>
      </view>
      <view class="collapse-content" v-show="isSupplyExpanded">
        <view class="supply-list">
          <view
            class="supply-item"
            v-for="(item, index) in planHerbList"
            :key="index"
          >
            <view class="item-header">
              <text class="material-name">{{ item.herbProductName }}</text>
              <text class="quantity"
                >{{ item.actualQuantity }}{{ planDetails.unitName }}</text
              >
            </view>
            <view class="item-details">
              <text class="batch-number">{{ item.productionBatchNo }}</text>
              <text class="order-number">{{ item.identificationCode }}</text>
            </view>
          </view>

          <!-- 实发总数量 -->
          <view class="total-section">
            <view class="total-label">实发总数量</view>
            <view class="total-value"
              >{{ planDetails.totalActualQuantity
              }}{{ planDetails.unitName }}</view
            >
          </view>
        </view>
      </view>
    </view>

    <!-- 收货信息表单 -->
    <view class="shipping-section" v-if="status === '2'">
      <view class="section-title">
        <text>收货作业</text>
      </view>
      <view class="shipping-info">
        <view class="info-item">
          <text class="info-label">实收数量</text>
          <view class="input-area">
            <input
              v-model="receivingForm.actualQuantity"
              type="number"
              placeholder="请输入"
              class="quantity-input"
              @input="onQuantityInput"
              @blur="onQuantityBlur"
            />
            <IconFont name="right" size="16" color="#31373D"></IconFont>
          </view>
        </view>
        <view class="error-text" v-if="errors.actualQuantity">
          {{ errors.actualQuantity }}
        </view>
      </view>
    </view>
    <!-- 收货信息表单 -->
    <view class="shipping-section" v-if="status === '3' && type === 'receive'">
      <view class="section-title">
        <text>收货作业</text>
      </view>
      <view class="shipping-info">
        <view class="info-item">
          <text class="info-label1">实收数量</text>
          <view class="input-area">
            <input
              v-model="planDetails.receiptQuantity"
              type="number"
              class="quantity-input"
              disabled
            />
          </view>
          <view
            class="info-value"
            style="
              display: flex;
              align-content: center;
              justify-content: center;
              line-height: 48rpx;
            "
            >{{ planDetails.unitName }}</view
          >
        </view>
      </view>
    </view>
    <!-- 保存按钮 -->
    <view class="save-section" v-if="status === '2'">
      <nut-button
        type="success"
        size="large"
        block
        :loading="saveLoading"
        @click="saveInfo"
        class="save-btn"
      >
        保存信息
      </nut-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import { IconFont } from "@nutui/icons-vue-taro";
import { queryDeliveryOrderDetail } from "@/api/receiving";
import { useReceivingStore } from "@/store/modules/receiving";
import { useReceivingFormStore } from "@/store/modules/receivingForm";

const router = useRouter();

const receivingForm = reactive({
  actualQuantity: "",
});

let id = ref("");
let status = ref<string>("");
const type = ref<string>("");
// 状态管理
const isPlanExpanded = ref(true);
const isSupplyExpanded = ref(true);
const saveLoading = ref(false);
const receivingStore = useReceivingStore();
const receivingFormStore = useReceivingFormStore();
let planHerbList = ref<any>([]);
let planDetails = ref<any>({});

// 表单验证错误
const errors = reactive({
  actualQuantity: "",
});

useDidShow(async () => {
  id.value = router.params.id ?? "";
  status.value = router.params.status ?? "";
  type.value = router.params.type ?? "";
  const res = await queryDeliveryOrderDetail(id.value);

  planDetails.value = res.data;
  planHerbList.value = res.data.herbList;

  if (res.data.herbList?.length > 0) {
    isSupplyExpanded.value = true;
  }
  console.log(
    receivingFormStore.getFormItem(planDetails.value.planCode)?.receiptQuantity
  );

  receivingForm.actualQuantity = receivingFormStore.getFormItem(
    planDetails.value.planCode
  )?.receiptQuantity;
});

// 切换计划信息展开状态
const togglePlan = () => {
  isPlanExpanded.value = !isPlanExpanded.value;
};

// 切换供货信息展开状态
const toggleSupply = () => {
  isSupplyExpanded.value = !isSupplyExpanded.value;
};

// 数量输入处理方法
const onQuantityInput = () => {
  // 清除错误信息
  errors.actualQuantity = "";
};

const onQuantityBlur = () => {
  // 可以在这里添加失焦时的验证逻辑
  if (
    receivingForm.actualQuantity &&
    receivingForm.actualQuantity.toString().trim() === ""
  ) {
    receivingForm.actualQuantity = "";
  }
};

// 表单验证
const validateForm = () => {
  errors.actualQuantity = "";

  if (!receivingForm.actualQuantity) {
    errors.actualQuantity = "实收数量不能为空";
    return false;
  }

  if (
    isNaN(Number(receivingForm.actualQuantity)) ||
    Number(receivingForm.actualQuantity) <= 0
  ) {
    errors.actualQuantity = "请输入有效的数量";
    return false;
  }

  return true;
};

// 保存信息
const saveInfo = async () => {
  if (!validateForm()) {
    return;
  }

  saveLoading.value = true;

  try {
    // 准备提交数据
    const submitData = {
      planNo: planDetails.value.planCode,
      receiptQuantity: receivingForm.actualQuantity,
    };
    // 将数据存到小程序缓存
    // Taro.setStorageSync("receivingForm", submitData);
    receivingFormStore.setFormItem(submitData);

    console.log("提交数据:", submitData);

    Taro.showToast({
      title: "保存成功",
      icon: "none",
      duration: 1000,
    });

    // 返回上一页
    setTimeout(() => {
      receivingStore.setIsEdit(true);

      Taro.navigateBack({
        delta: 1, // 返回层级
      });
    }, 400);
  } catch (error) {
    console.error("保存失败:", error);
    Taro.showToast({
      title: "保存失败，请重试",
      icon: "none",
    });
  } finally {
    saveLoading.value = false;
  }
};
</script>

<style lang="less">
.detail-form-page {
  min-height: 100vh;
  padding: 20rpx;
  // padding-bottom: 200rpx;
  // 防止闪屏的关键优化
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  will-change: auto;
  // 防止背景重绘
  background-attachment: local;

  .header-info {
    border-radius: 16rpx;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    padding: 16rpx 24rpx 1rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .info-content {
      flex: 1;
    }

    .info-item {
      margin-bottom: 16rpx;

      .label {
        color: rgba(29, 29, 29, 0.6);
        font-style: normal;
        font-weight: 400;
      }
      .label1 {
        color: #2f3133;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 600;
      }

      .value {
        color: #1d1d1d;
        font-style: normal;
        font-weight: 400;
      }
      .value1 {
        flex: 1;
        overflow-x: auto;
        color: #2f3133;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 50rpx;
        border-bottom: 1px solid #eef1f5;
      }
    }
  }

  .detail-section,
  .shipping-section {
    border-radius: 16rpx;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    margin-top: 20rpx;
  }

  .section-title {
    color: var(--, var(--, #1a43ad));
    font-style: normal;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    font-size: 32rpx;
    border-bottom: 1px solid #eef1f5;

    .collapse-arrow {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: rotate(0deg);
      transition: transform 0.2s ease;

      &.expanded {
        transform: rotate(180deg);
      }
    }
  }

  .collapse-content {
    background: white;
    // 防止闪屏优化
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }

  .plan-details {
    .info-item {
      display: flex;
      padding: 24rpx;
      border-bottom: 1px solid #eef1f5;
      // 防止闪屏优化
      transform: translateZ(0);
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        color: #2f3133;
        min-width: 160rpx;
        margin-right: 20rpx;
      }
      .info-label1 {
        color: #2f3133;
        min-width: 160rpx;
        margin-right: 20rpx;
      }

      .info-textarea {
        flex: 1;
        border-radius: 16rpx;
        background: #f5f7fa;
        height: 180rpx;
        // 防止闪屏优化
        transform: translateZ(0);
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        transition: none !important;
      }

      .info-value {
        color: #8d9094;
        flex: 1;
        line-height: 1.5;
        text-align: right;
      }
    }
    .info-bz {
      padding: 14rpx 0 14rpx 26rpx;
    }
    .info-textarea {
      border-radius: 16rpx;
      background: #f5f7fa;
      height: 180rpx;
      margin: 0 26rpx 26rpx 26rpx;
      // 防止闪屏优化
      transform: translateZ(0);
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
      transition: none !important;
    }
  }

  .supply-list {
    padding: 24rpx;
    // 防止闪屏优化
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;

    .supply-item {
      padding: 24rpx 0;
      border-bottom: 1px solid #eef1f5;
      // 防止闪屏优化
      transform: translateZ(0);
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;

      &:last-of-type {
        border-bottom: none;
      }

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8rpx;

        .material-name {
          font-size: 32rpx;
          color: #2f3133;
          font-weight: 500;
        }

        .quantity {
          font-size: 32rpx;
          color: #2f3133;
          font-weight: 500;
        }
      }

      .item-details {
        display: flex;
        flex-direction: column;
        gap: 4rpx;

        .batch-number,
        .order-number {
          font-size: 24rpx;
          color: #8d9094;
          word-break: break-all;

          word-wrap: break-word;
        }
      }
    }

    .total-section {
      padding-top: 24rpx;
      text-align: right;
      .total-label {
        font-size: 32rpx;
        color: #2f3133;
        font-weight: 500;
      }

      .total-value {
        font-size: 48rpx;
        color: #2f3133;
        font-weight: bold;
      }
    }
  }

  .shipping-info {
    .info-item {
      display: flex;
      padding: 24rpx;
      border-bottom: 1px solid #eef1f5;

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        color: #2f3133;
        min-width: 160rpx;
        margin-right: 20rpx;

        &::before {
          content: "*";
          color: #ff4d4f;
          margin-right: 4rpx;
        }
      }

      .input-area {
        display: flex;
        align-items: center;
        flex: 1;
        justify-content: flex-end;
        gap: 16rpx;

        .quantity-input {
          font-size: 28rpx;
          color: #2f3133;
          text-align: right;
          border: none;
          background: transparent;
          outline: none;
          width: 200rpx;

          &::placeholder {
            color: #8d9094;
            font-size: 28rpx;
          }
        }
      }
    }

    .error-text {
      font-size: 24rpx;
      color: #ff4d4f;
      margin: 8rpx 24rpx;
    }
  }

  .save-section {
    // position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 32rpx;
    background: white;
    border-top: 1rpx solid #eef1f5;

    .save-btn {
      height: 88rpx !important;
      border-radius: 44rpx !important;
      background: linear-gradient(180deg, #62f8ff 0%, #091899 100%),
        linear-gradient(180deg, #ffbf1a -33.33%, #e55f14 131.94%) !important;
      box-shadow: 1px 1px 6.1px 0px rgba(243, 198, 106, 0.3) !important;
      color: #fff !important;
      font-size: 28rpx !important;
      font-style: normal !important;
      font-weight: 600 !important;
      border: none !important;
    }
  }

  // 页面整体防闪屏优化
  .detail-form-page {
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    will-change: auto;
  }
}
</style>
